import logging
import time
import textwrap
import os
from typing import Optional, List
from playwright.sync_api import Page

from allm_helper.platforms.base_platform import BasePlatformDriver
from autils.pw_clipboard_helper import UniversalClipboardInterceptor

logger = logging.getLogger(__name__)


class YuanbaoDriver(BasePlatformDriver):
    """
    腾讯元宝平台的具体驱动实现。
    """
    CHAT_URL = "https://yuanbao.tencent.com/chat"

    def __init__(self, page: Page):
        """
        初始化腾讯元宝驱动，只接受 page 参数。
        模型配置将在 new_conversation 时传入。
        """
        super().__init__(page)
        self.page.goto(self.CHAT_URL)
        
        # 默认配置值
        self.current_model_config = {
            'system_prompt': '',
            'temperature': 0.7,
            'top_p': 0.9,
            'model_name': "混元大模型",  # 腾讯元宝默认模型
            'enable_web_search': False,  # 联网搜索
            'enable_deep_thinking': False,  # 深度思考
            'enable_auto_search': False,  # 自动搜索
            'use_ai_reading': False,  # AI阅读功能
        }
        
        # 状态追踪
        self.first_upload = True
        self.support_system_prompt = False
        self.initial_prompt_prefix = ""
        
        # 超时设置
        self.upload_files_timeout = 10000
        self.wait_reply_timeout = 60000
        
        # 响应格式
        self.context_format = "text"

    def _apply_conversation_settings(self):
        """应用会话设置，如模型、温度、系统提示等"""
        try:
            # 如果有系统提示词但不支持系统提示，则添加到初始提示前缀
            system_prompt = textwrap.dedent(self.current_model_config.get('system_prompt', ''))
            if system_prompt:
                if self.support_system_prompt:
                    self._update_system_prompt(system_prompt)
                else:
                    self.initial_prompt_prefix = f"{system_prompt}\n\n问题：\n"

            # 应用模型选择
            model_name = self.current_model_config.get('model_name')
            if model_name and model_name != "混元大模型":
                self.select_model(model_name)

            # 应用功能设置
            if self.current_model_config.get('enable_deep_thinking'):
                self.enable_deep_thinking(True)

            if self.current_model_config.get('enable_web_search'):
                self.enable_web_search(True)

            if self.current_model_config.get('enable_auto_search'):
                self.enable_auto_search(True)

        except Exception as e:
            logger.warning(f"应用会话设置失败: {e}")

    def _update_system_prompt(self, system_prompt):
        """更新系统提示词（如果平台支持）"""
        # 腾讯元宝可能不支持系统提示词，这里预留接口
        logger.warning("腾讯元宝暂不支持系统提示词设置")
        pass

    def select_model(self, model_name: str):
        """
        选择模型

        Args:
            model_name: 模型名称，支持 'DeepSeek', '深度思考', '混元大模型'
        """
        try:
            model_selectors = {
                'DeepSeek': [
                    'button:has-text("DeepSeek")',
                    'button[aria-label*="DeepSeek"]',
                    '.model-selector button:has-text("DeepSeek")'
                ],
                '深度思考': [
                    'button:has-text("深度思考")',
                    'button[aria-label*="深度思考"]',
                    '.model-selector button:has-text("深度思考")'
                ],
                '混元大模型': [
                    'button:has-text("混元")',
                    'button[aria-label*="混元"]',
                    '.model-selector button:has-text("混元")'
                ]
            }

            if model_name not in model_selectors:
                logger.warning(f"不支持的模型: {model_name}")
                return False

            for selector in model_selectors[model_name]:
                try:
                    model_btn = self.page.locator(selector)
                    if model_btn.is_visible():
                        model_btn.click()
                        logger.info(f"已切换到模型: {model_name}")
                        self.current_model_config['model_name'] = model_name
                        return True
                except:
                    continue

            logger.warning(f"未找到模型选择按钮: {model_name}")
            return False

        except Exception as e:
            logger.error(f"选择模型失败: {e}")
            return False

    def enable_deep_thinking(self, enabled: bool = True):
        """
        启用/禁用深度思考模式

        Args:
            enabled: 是否启用深度思考
        """
        try:
            deep_thinking_selectors = [
                'button:has-text("深度思考")',
                'button[aria-label*="深度思考"]',
                '.deep-thinking-toggle',
                'input[type="checkbox"][aria-label*="深度思考"]'
            ]

            for selector in deep_thinking_selectors:
                try:
                    element = self.page.locator(selector)
                    if element.is_visible():
                        # 检查当前状态
                        is_active = element.get_attribute('aria-pressed') == 'true' or \
                                   element.is_checked() if 'input' in selector else False

                        if is_active != enabled:
                            element.click()
                            logger.info(f"深度思考模式已{'启用' if enabled else '禁用'}")

                        self.current_model_config['enable_deep_thinking'] = enabled
                        return True
                except:
                    continue

            logger.warning("未找到深度思考控制按钮")
            return False

        except Exception as e:
            logger.error(f"设置深度思考模式失败: {e}")
            return False

    def enable_web_search(self, enabled: bool = True):
        """
        启用/禁用联网搜索功能

        Args:
            enabled: 是否启用联网搜索
        """
        try:
            web_search_selectors = [
                'button:has-text("联网搜索")',
                'button[aria-label*="联网搜索"]',
                '.web-search-toggle',
                'input[type="checkbox"][aria-label*="联网搜索"]'
            ]

            for selector in web_search_selectors:
                try:
                    element = self.page.locator(selector)
                    if element.is_visible():
                        # 检查当前状态
                        is_active = element.get_attribute('aria-pressed') == 'true' or \
                                   element.is_checked() if 'input' in selector else False

                        if is_active != enabled:
                            element.click()
                            logger.info(f"联网搜索功能已{'启用' if enabled else '禁用'}")

                        self.current_model_config['enable_web_search'] = enabled
                        return True
                except:
                    continue

            logger.warning("未找到联网搜索控制按钮")
            return False

        except Exception as e:
            logger.error(f"设置联网搜索功能失败: {e}")
            return False

    def enable_auto_search(self, enabled: bool = True):
        """
        启用/禁用自动搜索功能

        Args:
            enabled: 是否启用自动搜索
        """
        try:
            auto_search_selectors = [
                'button:has-text("自动搜索")',
                'button[aria-label*="自动搜索"]',
                '.auto-search-toggle',
                'input[type="checkbox"][aria-label*="自动搜索"]',
                # 根据界面提示，可能在页面底部
                '.bottom-controls button:has-text("自动")',
                '.search-mode-toggle'
            ]

            for selector in auto_search_selectors:
                try:
                    element = self.page.locator(selector)
                    if element.is_visible():
                        # 检查当前状态
                        is_active = element.get_attribute('aria-pressed') == 'true' or \
                                   element.is_checked() if 'input' in selector else False

                        if is_active != enabled:
                            element.click()
                            logger.info(f"自动搜索功能已{'启用' if enabled else '禁用'}")

                        self.current_model_config['enable_auto_search'] = enabled
                        return True
                except:
                    continue

            logger.warning("未找到自动搜索控制按钮")
            return False

        except Exception as e:
            logger.error(f"设置自动搜索功能失败: {e}")
            return False

    def start_temporary_chat(self):
        """
        开始临时对话（无需登录的对话模式）
        """
        try:
            # 检查是否已经在临时对话模式
            if self.page.url.endswith('/chat') and not self.page.locator('text=登录').is_visible():
                logger.info("已经在临时对话模式")
                return True

            # 尝试访问临时对话页面
            self.page.goto(self.CHAT_URL)

            # 等待页面加载
            self.page.wait_for_load_state('domcontentloaded')

            # 检查是否可以直接使用（无需登录）
            input_selectors = [
                '.ql-editor',
                'textarea[placeholder*="输入"]',
                'textarea[placeholder*="问题"]'
            ]

            for selector in input_selectors:
                try:
                    input_element = self.page.locator(selector)
                    if input_element.is_visible():
                        logger.info("临时对话模式已启动")
                        return True
                except:
                    continue

            logger.warning("无法启动临时对话模式，可能需要登录")
            return False

        except Exception as e:
            logger.error(f"启动临时对话失败: {e}")
            return False

    def _scroll_to_bottom(self, scroll_container):
        """滚动到容器底部"""
        try:
            if scroll_container.is_visible():
                scroll_container.evaluate('''(element) => {
                    element.scrollTop = element.scrollHeight;
                    const event = new Event('scroll');
                    element.dispatchEvent(event);
                }''')
        except Exception as e:
            logger.debug(f"滚动失败: {e}")

    def new_conversation(self, model_config: Optional[dict] = None):
        """
        创建新会话，并应用模型配置。

        Args:
            model_config: 模型配置字典，包含：
                - model_name: 模型名称
                - temperature: 温度参数
                - top_p: Top-P 参数
                - system_prompt: 系统提示词
                - 其他腾讯元宝特定参数
        """
        try:
            # 尝试点击新建对话按钮
            # 这里需要根据实际的腾讯元宝界面来调整选择器
            new_chat_selectors = [
                'button[aria-label="新建对话"]',
                'button:has-text("新建对话")',
                '.new-chat-btn',
                '[data-testid="new-chat"]'
            ]
            
            for selector in new_chat_selectors:
                try:
                    self.page.locator(selector).click(timeout=3000)
                    logger.info("成功点击新建对话按钮")
                    break
                except:
                    continue
            else:
                logger.info("未找到新建对话按钮，可能已经在新对话页面")
            
            self.initial_prompt_prefix = ""
            
            # 更新当前模型配置
            if model_config:
                self.current_model_config.update(model_config)
            
            self._apply_conversation_settings()
            
        except Exception as e:
            logger.error(f"创建新会话失败: {e}")
            
        return self

    def use_existing_conversation(self, conversation_title: str):
        """使用已有会话"""
        logger.info(f"正在查找并切换到会话: '{conversation_title}'...")
        try:
            # 这里需要根据腾讯元宝的实际界面来实现
            # 通常需要在历史对话列表中搜索
            logger.warning("腾讯元宝的历史对话切换功能待实现")
        except Exception as e:
            logger.error(f"切换到已有会话失败: {e}")
            raise

    def _validate_files(self, files):
        """验证文件格式和大小"""
        supported_formats = ['.pdf', '.doc', '.docx', '.txt', '.ppt', '.pptx', '.xls', '.xlsx']
        max_file_size = 100 * 1024 * 1024  # 100MB
        max_file_count = 50

        if len(files) > max_file_count:
            raise ValueError(f"文件数量超过限制，最多支持{max_file_count}个文件")

        for file_path in files:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")

            # 检查文件格式
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in supported_formats:
                raise ValueError(f"不支持的文件格式: {file_ext}，支持的格式: {', '.join(supported_formats)}")

            # 检查文件大小
            file_size = os.path.getsize(file_path)
            if file_size > max_file_size:
                raise ValueError(f"文件大小超过限制: {file_path}，最大支持100MB")

        return True

    def _upload_files(self, files, use_ai_reading=None):
        """
        上传文件

        Args:
            files: 文件路径列表
            use_ai_reading: 是否使用AI阅读功能，None表示使用配置中的设置
        """
        try:
            # 验证文件
            self._validate_files(files)

            # 确定是否使用AI阅读
            if use_ai_reading is None:
                use_ai_reading = self.current_model_config.get('use_ai_reading', False)

            if use_ai_reading:
                # 使用AI阅读功能上传
                return self._upload_files_with_ai_reading(files)
            else:
                # 使用普通上传功能
                return self._upload_files_normal(files)

        except Exception as e:
            logger.error(f"文件上传失败: {e}")
            return False

    def _upload_files_with_ai_reading(self, files):
        """使用AI阅读功能上传文件"""
        try:
            # 查找AI阅读上传区域
            ai_reading_selectors = [
                'div:has-text("AI阅读")',
                '.ai-reading-upload',
                'div:has-text("点击或拖放上传本地文件")',
                '.file-upload-area'
            ]

            for selector in ai_reading_selectors:
                try:
                    upload_area = self.page.locator(selector)
                    if upload_area.is_visible():
                        # 查找文件输入框
                        file_input = upload_area.locator('input[type="file"]').or_(
                            self.page.locator('input[type="file"]')
                        )

                        if file_input.count() > 0:
                            file_input.first.set_input_files(files)
                            logger.info(f"通过AI阅读功能成功上传 {len(files)} 个文件")
                            return True
                        else:
                            # 尝试点击上传区域触发文件选择
                            with self.page.expect_file_chooser() as fc_info:
                                upload_area.click()
                            file_chooser = fc_info.value
                            file_chooser.set_files(files)
                            logger.info(f"通过AI阅读功能成功上传 {len(files)} 个文件")
                            return True
                except:
                    continue

            logger.warning("未找到AI阅读上传功能")
            return False

        except Exception as e:
            logger.error(f"AI阅读文件上传失败: {e}")
            return False

    def _upload_files_normal(self, files):
        """使用普通方式上传文件"""
        try:
            # 查找普通上传按钮
            upload_selectors = [
                'button[aria-label="上传文件"]',
                'input[type="file"]',
                '.upload-btn',
                '[data-testid="upload"]',
                'button:has-text("上传")'
            ]

            for selector in upload_selectors:
                try:
                    if 'input[type="file"]' in selector:
                        # 直接设置文件
                        self.page.locator(selector).set_input_files(files)
                    else:
                        # 点击按钮触发文件选择
                        with self.page.expect_file_chooser() as fc_info:
                            self.page.locator(selector).click(timeout=3000)
                        file_chooser = fc_info.value
                        file_chooser.set_files(files)

                    logger.info(f"成功上传 {len(files)} 个文件")
                    return True
                except:
                    continue

            logger.warning("未找到文件上传功能")
            return False

        except Exception as e:
            logger.error(f"普通文件上传失败: {e}")
            return False

    def _wait_for_response_complete(self):
        """等待响应完成 - 等待最后一个回复框中出现复制按钮"""
        try:
            # 等待复制按钮出现，表示AI回复完成
            copy_button_selectors = [
                'button[aria-label="复制"]',
                'button:has-text("复制")',
                '.copy-btn',
                '[title="复制"]'
            ]

            for selector in copy_button_selectors:
                try:
                    # 等待复制按钮出现
                    self.page.wait_for_selector(selector, timeout=60000)
                    logger.debug(f"检测到复制按钮出现: {selector}")
                    return
                except Exception:
                    continue

            # 如果没有找到复制按钮，使用备用等待
            logger.debug("未找到复制按钮，使用备用等待")
            time.sleep(5)

        except Exception as e:
            logger.warning(f"等待响应完成时出错: {e}")
            time.sleep(3)

    def _get_reply(self):
        return self._get_reply_by_copy()


    def _get_reply_by_copy(self):
        """通过复制功能获取回复"""
        try:
            # 查找复制按钮
            copy_selectors = [
                'button[aria-label="复制"]',
                '.copy-btn',
                'button:has-text("复制")',
                '[data-testid="copy"]'
            ]
            
            for selector in copy_selectors:
                try:
                    copy_btn = self.page.locator(selector).last
                    if copy_btn.is_visible():
                        with UniversalClipboardInterceptor(self.page) as interceptor:
                            copy_btn.click()
                            interceptor.wait_for_capture(timeout=5.0)
                            return interceptor.text
                except:
                    continue
            
            raise Exception("未找到复制按钮")
            
        except Exception as e:
            logger.error(f"通过复制获取回复失败: {e}")
            raise

    def chat(self, prompt: str, attachments: Optional[List[str]] = None, 
             response_format: str = "text", **kwargs) -> str:
        """
        在当前会话中发送消息并获取回复。

        :param prompt: 用户输入的提示。
        :param attachments: 要上传的附件的本地文件路径列表。
        :param response_format: 响应格式，可选 "text"。
        :return: AI的回复文本。
        """
        # 首先切换到当前驱动的页面
        self.switch_to_page()

        if not self.is_ready():
            raise Exception("平台正忙，无法发送新消息。")

        try:
            # 如果有初始提示前缀（系统提示词），则拼接
            if self.initial_prompt_prefix:
                full_prompt = self.initial_prompt_prefix + prompt
                self.initial_prompt_prefix = ""  # 仅使用一次
            else:
                full_prompt = prompt

            # 1. 上传附件 (如果需要)
            if attachments:
                use_ai_reading = kwargs.get('use_ai_reading', self.current_model_config.get('use_ai_reading', False))
                upload_success = self._upload_files(attachments, use_ai_reading)
                if not upload_success:
                    logger.warning("文件上传失败，继续发送文本消息")

            # 2. 查找输入框并输入提示 - 基于实际观察到的结构
            input_selectors = [
                '.ql-editor',  # 腾讯元宝实际使用的输入框选择器
                'textarea[placeholder*="输入"]',
                'textarea[placeholder*="问题"]',
                '.chat-input textarea',
                'input[type="text"]',
                '[contenteditable="true"]'
            ]

            input_element = None
            for selector in input_selectors:
                try:
                    input_element = self.page.locator(selector).last
                    if input_element.is_visible():
                        break
                except:
                    continue

            if not input_element:
                raise Exception("未找到输入框")

            # 点击输入框激活
            input_element.click()
            # 输入文本
            input_element.fill(textwrap.dedent(full_prompt))

            # 3. 发送消息 - 基于实际观察到的结构
            send_selectors = [
                '#yuanbao-send-btn',  # 腾讯元宝实际使用的发送按钮ID
                'button[aria-label="发送"]',
                'button:has-text("发送")',
                '.send-btn',
                '[data-testid="send"]'
            ]

            for selector in send_selectors:
                try:
                    send_btn = self.page.locator(selector)
                    if send_btn.is_visible() and send_btn.is_enabled():
                        send_btn.click()
                        logger.info("消息已发送")
                        break
                except:
                    continue
            else:
                # 如果没找到发送按钮，尝试按回车键
                input_element.press("Enter")
                logger.info("通过回车键发送消息")

            # 4. 等待回复完成
            self._wait_for_response_complete()

            # 5. 获取回复
            return self._get_reply()

        except Exception as e:
            logger.error(f"聊天过程中出错: {e}")
            raise

    def is_ready(self) -> bool:
        """检查平台是否准备好接收新消息"""
        try:
            # 检查输入框是否可见且可用 - 基于实际观察到的结构
            input_selectors = [
                '.ql-editor',  # 腾讯元宝实际使用的输入框选择器
                'textarea[placeholder*="输入"]',
                'textarea[placeholder*="问题"]',
                '.chat-input textarea',
                'input[type="text"]'
            ]

            for selector in input_selectors:
                try:
                    input_element = self.page.locator(selector).last
                    if input_element.is_visible() and input_element.is_enabled():
                        return True
                except:
                    continue

            return False

        except Exception:
            return False

    def save_chat(self, chat_name: str):
        """保存当前会话并命名"""
        try:
            logger.warning("腾讯元宝的会话保存功能待实现")
        except Exception as e:
            logger.error(f"保存会话失败: {e}")

    def del_chat(self, chat_name: str):
        """删除会话"""
        logger.warning("腾讯元宝的会话删除功能待实现")
